'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ArrowRight, Calculator, FileText, TrendingUp, DollarSign } from 'lucide-react'

export default function FinancialPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/" 
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للرئيسية
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <Calculator className="h-6 w-6 text-blue-600" />
                </div>
                <h1 className="text-xl font-bold text-gray-900">الشؤون المالية</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            إدارة الشؤون المالية
          </h2>
          <p className="text-gray-600">
            اختر القسم المناسب لإدارة المداخيل والمصاريف والتقارير المالية
          </p>
        </div>

        {/* Financial Sections Grid */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {/* Income Table */}
          <Link href="/financial/income" className="group">
            <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105">
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <div className="bg-green-100 p-3 rounded-full group-hover:bg-green-200 transition-colors">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">جدول المداخيل</h3>
                  <p className="text-sm text-gray-600">إدارة مصادر الدخل</p>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  منحة الدولة
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  التبرعات والهدايا
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  الأنشطة المدرسية
                </div>
              </div>
            </div>
          </Link>

          {/* Daily Transactions */}
          <Link href="/financial/transactions" className="group">
            <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105">
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <div className="bg-blue-100 p-3 rounded-full group-hover:bg-blue-200 transition-colors">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">دفتر العمليات اليومية</h3>
                  <p className="text-sm text-gray-600">تسجيل المعاملات اليومية</p>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full ml-2"></span>
                  المقابيض والمصاريف
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full ml-2"></span>
                  الرصيد المتبقي
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full ml-2"></span>
                  الوثائق المؤيدة
                </div>
              </div>
            </div>
          </Link>

          {/* Financial Reports */}
          <Link href="/financial/reports" className="group">
            <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105">
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <div className="bg-purple-100 p-3 rounded-full group-hover:bg-purple-200 transition-colors">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">التقرير المالي</h3>
                  <p className="text-sm text-gray-600">تقارير مالية شاملة</p>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full ml-2"></span>
                  الموارد والنفقات
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full ml-2"></span>
                  تقارير الثلاثيات
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full ml-2"></span>
                  طباعة وتصدير
                </div>
              </div>
            </div>
          </Link>
        </div>

        {/* Quick Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المداخيل</p>
                <p className="text-2xl font-bold text-green-600">---</p>
                <p className="text-xs text-gray-500">دينار تونسي</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المصاريف</p>
                <p className="text-2xl font-bold text-red-600">---</p>
                <p className="text-xs text-gray-500">دينار تونسي</p>
              </div>
              <div className="bg-red-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الرصيد الحالي</p>
                <p className="text-2xl font-bold text-blue-600">---</p>
                <p className="text-xs text-gray-500">دينار تونسي</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">عدد المعاملات</p>
                <p className="text-2xl font-bold text-purple-600">---</p>
                <p className="text-xs text-gray-500">معاملة</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Calculator className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Information Box */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3 space-x-reverse">
            <div className="bg-blue-100 p-2 rounded-full">
              <Calculator className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-blue-900 mb-2">إرشادات الاستخدام</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• ابدأ بإدخال المداخيل في جدول المداخيل</li>
                <li>• سجل المعاملات اليومية في دفتر العمليات</li>
                <li>• راجع التقارير المالية للحصول على ملخص شامل</li>
                <li>• تأكد من حفظ البيانات بانتظام</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
