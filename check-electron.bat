@echo off
chcp 65001 >nul
echo ========================================
echo    فحص جاهزية تطبيق سطح المكتب
echo ========================================
echo.

echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    goto :error
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js: %%i
)

echo.
echo 🔍 فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    goto :error
) else (
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ npm: %%i
)

echo.
echo 🔍 فحص ملفات المشروع...
if not exist package.json (
    echo ❌ package.json غير موجود
    goto :error
) else (
    echo ✅ package.json موجود
)

if not exist electron\main.js (
    echo ❌ electron\main.js غير موجود
    goto :error
) else (
    echo ✅ electron\main.js موجود
)

if not exist src\app\page.tsx (
    echo ❌ ملفات Next.js ناقصة
    goto :error
) else (
    echo ✅ ملفات Next.js موجودة
)

echo.
echo 🔍 فحص المكتبات...
if not exist node_modules (
    echo ⚠️  المكتبات غير مثبتة
    echo 📦 شغّل: npm install
) else (
    echo ✅ المكتبات مثبتة
    
    echo 🔍 فحص Electron...
    if exist node_modules\electron (
        echo ✅ Electron مثبت
    ) else (
        echo ❌ Electron غير مثبت
        echo 📦 شغّل: npm install
    )
    
    echo 🔍 فحص electron-builder...
    if exist node_modules\electron-builder (
        echo ✅ electron-builder مثبت
    ) else (
        echo ❌ electron-builder غير مثبت
        echo 📦 شغّل: npm install
    )
)

echo.
echo 🔍 فحص إعدادات البناء...
findstr /C:"electron" package.json >nul
if %errorlevel% equ 0 (
    echo ✅ إعدادات Electron موجودة في package.json
) else (
    echo ❌ إعدادات Electron ناقصة
)

findstr /C:"build" package.json >nul
if %errorlevel% equ 0 (
    echo ✅ إعدادات البناء موجودة
) else (
    echo ❌ إعدادات البناء ناقصة
)

echo.
echo 🔍 فحص الأيقونات...
if exist electron\assets\icon.ico (
    echo ✅ أيقونة Windows موجودة
) else (
    echo ⚠️  أيقونة Windows غير موجودة (سيستخدم الافتراضية)
)

echo.
echo ========================================
echo           ملخص الفحص
echo ========================================
echo.
echo ✅ الجاهز:
echo    - Node.js و npm
echo    - ملفات المشروع الأساسية
echo    - إعدادات Electron
echo.
echo 📝 الخطوات التالية:
echo.
echo 🧪 للاختبار:
echo    dev-electron.bat
echo.
echo 🏗️ لبناء .exe:
echo    build-exe.bat
echo.
echo 📖 للمساعدة:
echo    DESKTOP_APP_GUIDE.md
echo.
goto :end

:error
echo.
echo ❌ يوجد مشاكل تحتاج حل
echo 📖 راجع DESKTOP_APP_GUIDE.md للمساعدة
echo.

:end
pause
