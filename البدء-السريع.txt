🚀 دليل البدء السريع - نظام إدارة الشؤون المالية المدرسية

═══════════════════════════════════════════════════════════════

📋 قائمة المهام (5 خطوات فقط):

□ 1. تثبيت Node.js
□ 2. إعداد Supabase  
□ 3. تحديث ملف البيئة
□ 4. تشغيل المشروع
□ 5. اختبار النظام

═══════════════════════════════════════════════════════════════

🔧 الخطوة 1: تثبيت Node.js
   
   • اذهب إلى: https://nodejs.org
   • حمّل النسخة LTS (الموصى بها)
   • ثبّت البرنامج (اختر "Add to PATH")
   • أعد تشغيل Command Prompt
   • اختبر: اكتب "node --version" في Command Prompt

═══════════════════════════════════════════════════════════════

🗄️ الخطوة 2: إعداد Supabase

   • اذهب إلى: https://supabase.com
   • انقر "Start your project"
   • سجّل دخول أو أنشئ حساب
   • انقر "New Project"
   • املأ البيانات:
     - الاسم: School Finance System
     - كلمة المرور: اختر كلمة قوية (احفظها!)
     - المنطقة: اختر الأقرب لك
   • انتظر 2-3 دقائق حتى ينتهي الإنشاء

═══════════════════════════════════════════════════════════════

🔑 الخطوة 3: الحصول على المفاتيح

   في Supabase Dashboard:
   • اذهب إلى Settings > API
   • انسخ "Project URL" 
   • انسخ "anon public key"
   
   ثم:
   • افتح ملف ".env.local" في مجلد المشروع
   • استبدل "your_supabase_project_url" بالـ URL الحقيقي
   • استبدل "your_supabase_anon_key" بالمفتاح الحقيقي
   • احفظ الملف

═══════════════════════════════════════════════════════════════

🗃️ الخطوة 4: إعداد قاعدة البيانات

   في Supabase Dashboard:
   • اذهب إلى SQL Editor
   • انقر "New query"
   • افتح ملف "supabase-schema.sql" من مجلد المشروع
   • انسخ المحتوى كاملاً
   • الصق في SQL Editor
   • انقر "Run" (أو اضغط Ctrl+Enter)
   • انتظر حتى ينتهي التنفيذ

═══════════════════════════════════════════════════════════════

▶️ الخطوة 5: تشغيل المشروع

   في مجلد المشروع:
   • افتح Command Prompt أو PowerShell
   • اكتب: npm install
   • انتظر حتى ينتهي التثبيت
   • اكتب: npm run dev
   • افتح المتصفح على: http://localhost:3000

═══════════════════════════════════════════════════════════════

✅ اختبار النظام:

   يجب أن ترى:
   • صفحة رئيسية باللغة العربية
   • زرين: "معطيات المدرسة" و "الشؤون المالية"
   • تصميم أنيق مع دعم RTL

   جرب:
   • انقر "معطيات المدرسة" وأدخل بيانات
   • انقر "الشؤون المالية" > "جدول المداخيل"
   • أضف مدخول جديد
   • اطبع التقرير

═══════════════════════════════════════════════════════════════

🆘 إذا واجهت مشاكل:

   • شغّل "check-setup.bat" للفحص السريع
   • راجع "TROUBLESHOOTING.md" للحلول
   • تأكد من اتباع الخطوات بالترتيب

═══════════════════════════════════════════════════════════════

📞 ملفات المساعدة:

   • INSTALLATION_GUIDE.md - دليل مفصل
   • QUICK_START.md - بدء سريع
   • TROUBLESHOOTING.md - حل المشاكل
   • README.md - معلومات شاملة

═══════════════════════════════════════════════════════════════

🎯 بعد التشغيل الناجح:

   1. أدخل معلومات مدرستك في "معطيات المدرسة"
   2. حدد تواريخ الثلاثيات الأربعة
   3. ابدأ بإدخال المداخيل في "جدول المداخيل"
   4. سجّل المعاملات في "دفتر العمليات اليومية"
   5. أنشئ تقاريرك في "التقرير المالي"

═══════════════════════════════════════════════════════════════

🔒 ملاحظات أمنية:

   • لا تشارك ملف .env.local مع أحد
   • احفظ كلمة مرور Supabase في مكان آمن
   • اعمل نسخة احتياطية من البيانات بانتظام

═══════════════════════════════════════════════════════════════

✨ مبروك! نظامك جاهز للاستخدام
