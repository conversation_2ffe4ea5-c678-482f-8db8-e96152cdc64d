@echo off
chcp 65001 >nul
echo ========================================
echo    بناء تطبيق سطح المكتب (.exe)
echo ========================================
echo.

echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

echo.
echo 🔍 التحقق من المكتبات...
if not exist node_modules (
    echo 📦 تثبيت المكتبات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
) else (
    echo ✅ المكتبات مثبتة
)

echo.
echo 🏗️ بناء التطبيق...
echo المرحلة 1: بناء Next.js...
npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء Next.js
    pause
    exit /b 1
)

echo المرحلة 2: تصدير الملفات الثابتة...
npm run export
if %errorlevel% neq 0 (
    echo ❌ فشل في تصدير الملفات
    pause
    exit /b 1
)

echo المرحلة 3: بناء تطبيق Electron...
npm run dist
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء Electron
    pause
    exit /b 1
)

echo.
echo ========================================
echo           🎉 تم البناء بنجاح!
echo ========================================
echo.
echo 📁 ملف .exe موجود في: dist\
echo 📦 ملف التثبيت: dist\نظام إدارة الشؤون المالية المدرسية Setup.exe
echo.
echo 💡 لتشغيل التطبيق مباشرة:
echo    - اذهب إلى مجلد dist
echo    - شغّل ملف Setup.exe
echo    - اتبع تعليمات التثبيت
echo.
echo 🔧 لتطوير التطبيق:
echo    npm run electron-dev
echo.
pause
