# دليل التثبيت والإعداد الشامل

## الخطوة 1: تثبيت Node.js

### لنظام Windows:
1. اذهب إلى https://nodejs.org
2. حمّل النسخة LTS (الموصى بها)
3. شغّل ملف التثبيت (.msi)
4. اتبع التعليمات (اختر "Add to PATH" إذا ظهر)
5. أعد تشغيل Command Prompt أو PowerShell

### للتحقق من التثبيت:
```bash
node --version
npm --version
```

## الخطوة 2: إعداد Supabase

### إنشاء حساب Supabase:
1. اذهب إلى https://supabase.com
2. انقر على "Start your project"
3. سجّل دخول بـ GitHub أو أنشئ حساب جديد
4. انقر على "New Project"

### إعداد المشروع:
1. **اسم المشروع**: School Finance System
2. **كلمة مرور قاعدة البيانات**: اختر كلمة مرور قوية (احفظها!)
3. **المنطقة**: اختر الأقرب لك (مثل: Europe West)
4. انقر على "Create new project"

### الحصول على بيانات الاتصال:
1. انتظر حتى ينتهي إنشاء المشروع (2-3 دقائق)
2. اذهب إلى **Settings** > **API**
3. انسخ:
   - **Project URL**:(https://adhvhiiiymqomyhdmxee.supabase.co)co`
   - **anon public key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFkaHZoaWlpeW1xb215aGRteGVlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTI2OTUsImV4cCI6MjA2MzkyODY5NX0.vlqxQ3MAwQTHG_75tmPYou7dOhUi_ffvH6oluEyGsA4

## الخطوة 3: إعداد قاعدة البيانات

### في Supabase Dashboard:
1. اذهب إلى **SQL Editor**
2. انقر على "New query"
3. انسخ محتوى ملف `supabase-schema.sql` بالكامل
4. الصق في المحرر
5. انقر على "Run" (أو Ctrl+Enter)

### التحقق من إنشاء الجداول:
1. اذهب إلى **Table Editor**
2. يجب أن ترى 4 جداول:
   - school_info
   - academic_periods
   - income_records
   - daily_transactions

## الخطوة 4: تشغيل المشروع

### في مجلد المشروع:
```bash
# تثبيت المكتبات
npm install

# تحديث ملف البيئة
# عدّل .env.local بمعلومات Supabase

# تشغيل المشروع
npm run dev
```

### تحديث ملف .env.local:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

## الخطوة 5: اختبار النظام

### افتح المتصفح:
- اذهب إلى http://localhost:3000
- يجب أن ترى الصفحة الرئيسية باللغة العربية

### اختبر الوظائف:
1. **معطيات المدرسة**: أدخل معلومات المدرسة
2. **جدول المداخيل**: أضف مدخول جديد
3. **دفتر العمليات**: سجّل معاملة
4. **التقرير المالي**: اطبع تقرير

## استكشاف الأخطاء الشائعة

### خطأ "node is not recognized":
- تأكد من تثبيت Node.js
- أعد تشغيل Command Prompt
- تأكد من إضافة Node.js إلى PATH

### خطأ في الاتصال بـ Supabase:
- تحقق من URL و API Key في .env.local
- تأكد من عدم وجود مسافات إضافية
- تأكد من تشغيل SQL Schema

### خطأ في تثبيت المكتبات:
```bash
# امسح node_modules وأعد التثبيت
rm -rf node_modules
npm install
```

### خطأ في الصلاحيات:
- تأكد من تشغيل جميع استعلامات SQL
- تحقق من إعدادات RLS في Supabase

## نصائح مهمة

1. **احفظ كلمة مرور قاعدة البيانات** في مكان آمن
2. **لا تشارك API Keys** مع أحد
3. **اعمل نسخة احتياطية** من البيانات بانتظام
4. **استخدم HTTPS** في الإنتاج

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من Console في المتصفح (F12)
2. راجع Supabase Logs في Dashboard
3. تأكد من صحة جميع الخطوات أعلاه

## الخطوات التالية

بعد التشغيل الناجح:
1. أدخل معلومات مدرستك
2. حدد الفترات الدراسية
3. ابدأ بتسجيل المداخيل والمصاريف
4. أنشئ تقاريرك المالية

---

**ملاحظة**: هذا النظام جاهز للاستخدام الفوري ويحتوي على بيانات تجريبية للاختبار.
