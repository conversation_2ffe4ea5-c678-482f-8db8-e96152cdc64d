'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowRight, Save, School, Calendar } from 'lucide-react'
import { schoolInfoService, academicPeriodsService } from '@/lib/supabase'
import { SchoolInfo, AcademicPeriod } from '@/types/database'
import SchoolInfoForm from '@/components/SchoolInfoForm'
import AcademicPeriodsForm from '@/components/AcademicPeriodsForm'

export default function SchoolInfoPage() {
  const router = useRouter()
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo | null>(null)
  const [academicPeriods, setAcademicPeriods] = useState<AcademicPeriod[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'school' | 'periods'>('school')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [schoolData, periodsData] = await Promise.all([
        schoolInfoService.getSchoolInfo(),
        academicPeriodsService.getAcademicPeriods()
      ])
      
      setSchoolInfo(schoolData)
      setAcademicPeriods(periodsData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSchoolInfoSave = async (data: any) => {
    try {
      setSaving(true)
      const savedData = await schoolInfoService.upsertSchoolInfo(data)
      setSchoolInfo(savedData)
      alert('تم حفظ معلومات المدرسة بنجاح')
    } catch (error) {
      console.error('Error saving school info:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handlePeriodSave = async (periodData: any) => {
    try {
      setSaving(true)
      await academicPeriodsService.upsertAcademicPeriod(periodData)
      await loadData() // Reload to get updated data
      alert('تم حفظ الفترة الدراسية بنجاح')
    } catch (error) {
      console.error('Error saving academic period:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/" 
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للرئيسية
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-green-100 p-2 rounded-lg">
                  <School className="h-6 w-6 text-green-600" />
                </div>
                <h1 className="text-xl font-bold text-gray-900">معطيات المدرسة</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 space-x-reverse">
              <button
                onClick={() => setActiveTab('school')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'school'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <School className="h-4 w-4" />
                  <span>معلومات المدرسة</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('periods')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'periods'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Calendar className="h-4 w-4" />
                  <span>الفترات الدراسية</span>
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'school' && (
          <div className="card">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                معلومات المدرسة الأساسية
              </h2>
              <p className="text-gray-600">
                قم بإدخال وتحديث معلومات المدرسة والإدارة
              </p>
            </div>
            <SchoolInfoForm 
              initialData={schoolInfo}
              onSave={handleSchoolInfoSave}
              saving={saving}
            />
          </div>
        )}

        {activeTab === 'periods' && (
          <div className="card">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                الفترات الدراسية
              </h2>
              <p className="text-gray-600">
                قم بتحديد تواريخ بداية ونهاية كل فترة دراسية
              </p>
            </div>
            <AcademicPeriodsForm 
              periods={academicPeriods}
              onSave={handlePeriodSave}
              saving={saving}
            />
          </div>
        )}
      </main>
    </div>
  )
}
