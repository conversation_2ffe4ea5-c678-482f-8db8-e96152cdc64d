@echo off
echo ========================================
echo    نظام إدارة الشؤون المالية المدرسية
echo ========================================
echo.

echo التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo يرجى تثبيت Node.js من https://nodejs.org
    echo ثم أعد تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

echo التحقق من ملف البيئة...
if not exist .env.local (
    echo ❌ ملف .env.local غير موجود!
    echo يرجى إنشاء ملف .env.local وإضافة معلومات Supabase
    echo راجع ملف INSTALLATION_GUIDE.md للتفاصيل
    pause
    exit /b 1
)

echo ✅ ملف البيئة موجود
echo.

echo تثبيت المكتبات...
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

echo تشغيل المشروع...
echo 🚀 سيتم فتح المتصفح على http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

npm run dev
