export interface Database {
  public: {
    Tables: {
      school_info: {
        Row: {
          id: string
          school_name: string
          state_name: string
          district_name: string
          principal_name: string
          association_president: string
          treasurer_name: string
          number_of_classes: number
          number_of_students: number
          academic_year: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_name: string
          state_name: string
          district_name: string
          principal_name: string
          association_president: string
          treasurer_name: string
          number_of_classes: number
          number_of_students: number
          academic_year: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_name?: string
          state_name?: string
          district_name?: string
          principal_name?: string
          association_president?: string
          treasurer_name?: string
          number_of_classes?: number
          number_of_students?: number
          academic_year?: string
          updated_at?: string
        }
      }
      academic_periods: {
        Row: {
          id: string
          period_number: number
          period_name: string
          start_date: string
          end_date: string
          academic_year: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          period_number: number
          period_name: string
          start_date: string
          end_date: string
          academic_year: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          period_number?: number
          period_name?: string
          start_date?: string
          end_date?: string
          academic_year?: string
          updated_at?: string
        }
      }
      income_records: {
        Row: {
          id: string
          category: string
          amount: number
          notes: string | null
          academic_year: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category: string
          amount: number
          notes?: string | null
          academic_year: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category?: string
          amount?: number
          notes?: string | null
          academic_year?: string
          updated_at?: string
        }
      }
      daily_transactions: {
        Row: {
          id: string
          transaction_type: string
          income: number | null
          expense: number | null
          remaining_balance: number
          transaction_date: string
          document_number: string | null
          academic_year: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          transaction_type: string
          income?: number | null
          expense?: number | null
          remaining_balance: number
          transaction_date: string
          document_number?: string | null
          academic_year: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          transaction_type?: string
          income?: number | null
          expense?: number | null
          remaining_balance?: number
          transaction_date?: string
          document_number?: string | null
          academic_year?: string
          updated_at?: string
        }
      }
    }
  }
}

export type SchoolInfo = Database['public']['Tables']['school_info']['Row']
export type AcademicPeriod = Database['public']['Tables']['academic_periods']['Row']
export type IncomeRecord = Database['public']['Tables']['income_records']['Row']
export type DailyTransaction = Database['public']['Tables']['daily_transactions']['Row']
