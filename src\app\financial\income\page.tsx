'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { ArrowRight, Plus, Printer, Save, Trash2, Edit } from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import { incomeService, schoolInfoService } from '@/lib/supabase'
import { IncomeRecord, SchoolInfo } from '@/types/database'

const INCOME_CATEGORIES = [
  'باب منحة الدّولة: التّعهّد و الصّيانة',
  'باب منحة الحليب لفائدة العامل',
  'باب التّبرّعات و الهدايا',
  'باب حصّة المدرسة من دروس التّدارك',
  'باب مداخيل متعلّقة بأنشطة مدرسيّة',
  'باب مداخيل كراء المشرب (إن وجد)'
]

export default function IncomePage() {
  const [incomeRecords, setIncomeRecords] = useState<IncomeRecord[]>([])
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [editingRecord, setEditingRecord] = useState<IncomeRecord | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    category: '',
    amount: 0,
    notes: ''
  })
  const printRef = useRef<HTMLDivElement>(null)

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'جدول المداخيل'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [incomeData, schoolData] = await Promise.all([
        incomeService.getIncomeRecords(),
        schoolInfoService.getSchoolInfo()
      ])
      setIncomeRecords(incomeData)
      setSchoolInfo(schoolData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      if (!formData.category || formData.amount <= 0) {
        alert('يرجى إدخال جميع البيانات المطلوبة')
        return
      }

      const recordData = {
        ...formData,
        academic_year: schoolInfo?.academic_year || new Date().getFullYear() + '-' + (new Date().getFullYear() + 1),
        ...(editingRecord && { id: editingRecord.id })
      }

      await incomeService.upsertIncomeRecord(recordData)
      await loadData()
      resetForm()
      alert('تم حفظ البيانات بنجاح')
    } catch (error) {
      console.error('Error saving record:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    }
  }

  const handleEdit = (record: IncomeRecord) => {
    setEditingRecord(record)
    setFormData({
      category: record.category,
      amount: record.amount,
      notes: record.notes || ''
    })
    setShowAddForm(true)
  }

  const handleDelete = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
      try {
        await incomeService.deleteIncomeRecord(id)
        await loadData()
        alert('تم حذف السجل بنجاح')
      } catch (error) {
        console.error('Error deleting record:', error)
        alert('حدث خطأ أثناء حذف السجل')
      }
    }
  }

  const resetForm = () => {
    setFormData({ category: '', amount: 0, notes: '' })
    setEditingRecord(null)
    setShowAddForm(false)
  }

  const getTotalAmount = () => {
    return incomeRecords.reduce((total, record) => total + record.amount, 0)
  }

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('ar-TN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b no-print">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/financial" 
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للشؤون المالية
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900">جدول المداخيل</h1>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <button
                onClick={() => setShowAddForm(true)}
                className="btn-primary flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="h-4 w-4" />
                <span>إضافة مدخول</span>
              </button>
              <button
                onClick={handlePrint}
                className="btn-secondary flex items-center space-x-2 space-x-reverse"
              >
                <Printer className="h-4 w-4" />
                <span>طباعة</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="card mb-8 no-print">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {editingRecord ? 'تعديل المدخول' : 'إضافة مدخول جديد'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="form-label">مصدر المدخول *</label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className="form-input"
                  required
                >
                  <option value="">اختر مصدر المدخول</option>
                  {INCOME_CATEGORIES.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="form-label">المبلغ (بالدينار) *</label>
                <input
                  type="number"
                  step="0.001"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                  className="form-input"
                  required
                  min="0"
                />
              </div>
              <div>
                <label className="form-label">ملاحظات</label>
                <input
                  type="text"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  className="form-input"
                  placeholder="ملاحظات إضافية"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 space-x-reverse">
              <button
                onClick={resetForm}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="btn-primary flex items-center space-x-2 space-x-reverse"
              >
                <Save className="h-4 w-4" />
                <span>حفظ</span>
              </button>
            </div>
          </div>
        )}

        {/* Printable Content */}
        <div ref={printRef} className="bg-white">
          {/* Print Header */}
          <div className="print-title">
            جمعية العمل التنموي - مشروع التصرّف الماليّ لسنة {schoolInfo?.academic_year} بـالمدرسة الإبتدائية {schoolInfo?.school_name}
          </div>
          <div className="print-subtitle">
            التاريخ: {getCurrentDate()}
          </div>

          {/* Income Table */}
          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th className="w-1/2">مصادر المداخيل</th>
                  <th className="w-1/4">المبالغ (بالدينار)</th>
                  <th className="w-1/4">ملاحظات</th>
                  <th className="w-24 no-print">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {incomeRecords.map((record) => (
                  <tr key={record.id}>
                    <td className="font-medium">{record.category}</td>
                    <td className="text-center">{record.amount.toFixed(3)}</td>
                    <td>{record.notes || '-'}</td>
                    <td className="no-print">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleEdit(record)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(record.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
                {/* Total Row */}
                <tr className="bg-gray-50 font-bold">
                  <td>الجملة</td>
                  <td className="text-center">{getTotalAmount().toFixed(3)}</td>
                  <td>-</td>
                  <td className="no-print">-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  )
}
