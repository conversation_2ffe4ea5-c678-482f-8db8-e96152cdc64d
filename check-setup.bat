@echo off
chcp 65001 >nul
echo ========================================
echo    فحص إعداد نظام الشؤون المالية
echo ========================================
echo.

echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 حمّل من: https://nodejs.org
    goto :error
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js مثبت: %%i
)

echo.
echo 🔍 فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    goto :error
) else (
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ npm متوفر: %%i
)

echo.
echo 🔍 فحص ملف البيئة...
if not exist .env.local (
    echo ❌ ملف .env.local غير موجود
    echo 📝 أنشئ الملف وأضف معلومات Supabase
    goto :error
) else (
    echo ✅ ملف .env.local موجود
)

echo.
echo 🔍 فحص محتوى ملف البيئة...
findstr /C:"your_supabase_project_url" .env.local >nul
if %errorlevel% equ 0 (
    echo ⚠️  يحتاج تحديث: لم يتم تحديث URL
    echo 📝 حدّث NEXT_PUBLIC_SUPABASE_URL
)

findstr /C:"your_supabase_anon_key" .env.local >nul
if %errorlevel% equ 0 (
    echo ⚠️  يحتاج تحديث: لم يتم تحديث API Key
    echo 📝 حدّث NEXT_PUBLIC_SUPABASE_ANON_KEY
)

echo.
echo 🔍 فحص ملفات المشروع...
if not exist package.json (
    echo ❌ package.json غير موجود
    goto :error
) else (
    echo ✅ package.json موجود
)

if not exist src\app\page.tsx (
    echo ❌ ملفات المشروع ناقصة
    goto :error
) else (
    echo ✅ ملفات المشروع موجودة
)

echo.
echo 🔍 فحص المكتبات...
if not exist node_modules (
    echo ⚠️  المكتبات غير مثبتة
    echo 📦 شغّل: npm install
) else (
    echo ✅ المكتبات مثبتة
)

echo.
echo ========================================
echo           ملخص الفحص
echo ========================================
echo.
echo ✅ الأشياء الجاهزة:
echo    - Node.js مثبت
echo    - ملفات المشروع موجودة
echo.
echo 📝 ما تحتاج لفعله:
echo    1. إعداد حساب Supabase
echo    2. تحديث ملف .env.local
echo    3. تشغيل SQL Schema في Supabase
echo    4. تشغيل: npm install
echo    5. تشغيل: npm run dev
echo.
echo 📖 للمساعدة: راجع INSTALLATION_GUIDE.md
echo.
goto :end

:error
echo.
echo ❌ يوجد مشاكل تحتاج حل
echo 📖 راجع INSTALLATION_GUIDE.md للمساعدة
echo.

:end
pause
