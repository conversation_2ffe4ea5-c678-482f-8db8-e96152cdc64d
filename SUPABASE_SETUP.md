# دليل إعداد Supabase

## الخطوة 1: إنشاء مشروع Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. انقر على "Start your project"
3. قم بإنشاء حساب جديد أو تسجيل الدخول
4. انقر على "New Project"
5. اختر Organization أو أنشئ واحدة جديدة
6. أدخل تفاصيل المشروع:
   - **Name**: School Finance System
   - **Database Password**: كلمة مرور قوية
   - **Region**: اختر المنطقة الأقرب لك
7. انقر على "Create new project"

## الخطوة 2: الحصول على بيانات الاتصال

1. بعد إنشاء المشروع، اذهب إلى **Settings** > **API**
2. انسخ القيم التالية:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## الخطوة 3: تحديث ملف البيئة

1. افتح ملف `.env.local` في مجلد المشروع
2. استبدل القيم بالبيانات الفعلية:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
1980```

## الخطوة 4: إنشاء قاعدة البيانات

1. في Supabase Dashboard، اذهب إلى **SQL Editor**
2. انقر على "New query"
3. انسخ محتوى ملف `supabase-schema.sql` بالكامل
4. الصق المحتوى في محرر SQL
5. انقر على "Run" لتنفيذ الاستعلام

## الخطوة 5: التحقق من إنشاء الجداول

1. اذهب إلى **Table Editor**
2. تأكد من وجود الجداول التالية:
   - `school_info`
   - `academic_periods`
   - `income_records`
   - `daily_transactions`

## الخطوة 6: إعداد Row Level Security (RLS)

الجداول محمية بـ RLS افتراضياً. للتطوير، تم إنشاء policies تسمح بالوصول الكامل.

**للإنتاج**: يجب تحديث الـ policies لتكون أكثر أماناً:

```sql
-- مثال على policy أكثر أماناً
CREATE POLICY "Users can only access their school data" 
ON school_info FOR ALL 
USING (auth.uid() = user_id);
```

## الخطوة 7: اختبار الاتصال

1. شغل المشروع محلياً: `npm run dev`
2. افتح [http://localhost:3000](http://localhost:3000)
3. اذهب إلى قسم "معطيات المدرسة"
4. جرب إدخال بيانات جديدة
5. تأكد من حفظ البيانات في Supabase

## استكشاف الأخطاء

### خطأ في الاتصال
- تأكد من صحة URL و API Key
- تأكد من عدم وجود مسافات إضافية في ملف `.env.local`

### خطأ في الصلاحيات
- تأكد من تشغيل SQL Schema بالكامل
- تحقق من إعدادات RLS في Table Editor

### خطأ في البيانات
- تأكد من تطابق أسماء الجداول والأعمدة
- راجع Console في المتصفح للأخطاء

## نصائح إضافية

1. **النسخ الاحتياطي**: استخدم Supabase CLI لعمل نسخ احتياطية منتظمة
2. **المراقبة**: راقب استخدام قاعدة البيانات في Dashboard
3. **الأمان**: في الإنتاج، استخدم authentication وحدد صلاحيات دقيقة
4. **الأداء**: أضف indexes للاستعلامات المتكررة

## الدعم

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Community](https://github.com/supabase/supabase/discussions)
- [Next.js with Supabase Guide](https://supabase.com/docs/guides/getting-started/quickstarts/nextjs)
