'use client'

import { useState, useEffect } from 'react'
import { Save, Calendar } from 'lucide-react'
import { AcademicPeriod } from '@/types/database'

interface AcademicPeriodsFormProps {
  periods: AcademicPeriod[]
  onSave: (periodData: any) => Promise<void>
  saving: boolean
}

const defaultPeriods = [
  { number: 1, name: 'الثلاثي الأول' },
  { number: 2, name: 'الثلاثي الثاني' },
  { number: 3, name: 'الثلاثي الثالث' },
  { number: 4, name: 'الثلاثي الرابع' }
]

export default function AcademicPeriodsForm({ periods, onSave, saving }: AcademicPeriodsFormProps) {
  const [periodsData, setPeriodsData] = useState<any[]>([])

  useEffect(() => {
    // Initialize periods data
    const initialData = defaultPeriods.map(defaultPeriod => {
      const existingPeriod = periods.find(p => p.period_number === defaultPeriod.number)
      return {
        id: existingPeriod?.id || null,
        period_number: defaultPeriod.number,
        period_name: defaultPeriod.name,
        start_date: existingPeriod?.start_date || '',
        end_date: existingPeriod?.end_date || '',
        academic_year: existingPeriod?.academic_year || new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)
      }
    })
    setPeriodsData(initialData)
  }, [periods])

  const handleDateChange = (periodNumber: number, field: 'start_date' | 'end_date', value: string) => {
    setPeriodsData(prev => prev.map(period => 
      period.period_number === periodNumber 
        ? { ...period, [field]: value }
        : period
    ))
  }

  const handleSavePeriod = async (periodNumber: number) => {
    const periodData = periodsData.find(p => p.period_number === periodNumber)
    if (periodData && periodData.start_date && periodData.end_date) {
      await onSave(periodData)
    } else {
      alert('يرجى إدخال تاريخ البداية والنهاية')
    }
  }

  const formatDateForInput = (dateString: string) => {
    if (!dateString) return ''
    return dateString.split('T')[0]
  }

  return (
    <div className="space-y-6">
      {periodsData.map((period) => (
        <div key={period.period_number} className="border border-gray-200 rounded-lg p-6 bg-gray-50">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <div className="bg-primary-100 p-2 rounded-lg">
              <Calendar className="h-5 w-5 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              {period.period_name}
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* Start Date */}
            <div>
              <label className="form-label">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formatDateForInput(period.start_date)}
                onChange={(e) => handleDateChange(period.period_number, 'start_date', e.target.value)}
                className="form-input"
                required
              />
            </div>

            {/* End Date */}
            <div>
              <label className="form-label">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                value={formatDateForInput(period.end_date)}
                onChange={(e) => handleDateChange(period.period_number, 'end_date', e.target.value)}
                className="form-input"
                required
              />
            </div>
          </div>

          {/* Save Button for this period */}
          <div className="flex justify-end">
            <button
              type="button"
              onClick={() => handleSavePeriod(period.period_number)}
              disabled={saving || !period.start_date || !period.end_date}
              className="btn-primary flex items-center space-x-2 space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>جاري الحفظ...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>حفظ {period.period_name}</span>
                </>
              )}
            </button>
          </div>
        </div>
      ))}

      {/* Information Box */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3 space-x-reverse">
          <div className="bg-blue-100 p-2 rounded-full">
            <Calendar className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h4 className="font-medium text-blue-900 mb-1">ملاحظة مهمة</h4>
            <p className="text-sm text-blue-700">
              يرجى التأكد من صحة التواريخ المدخلة. هذه التواريخ ستستخدم في التقارير المالية والإحصائيات.
              يمكنك حفظ كل فترة على حدة أو تعديل التواريخ في أي وقت.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
