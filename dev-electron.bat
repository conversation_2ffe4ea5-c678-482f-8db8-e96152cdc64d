@echo off
chcp 65001 >nul
echo ========================================
echo    تشغيل التطبيق في وضع التطوير
echo ========================================
echo.

echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    pause
    exit /b 1
)

echo 🔍 التحقق من المكتبات...
if not exist node_modules (
    echo 📦 تثبيت المكتبات...
    npm install
)

echo.
echo 🚀 تشغيل التطبيق في وضع التطوير...
echo.
echo 💡 سيتم:
echo    1. تشغيل Next.js على http://localhost:3000
echo    2. فتح نافذة Electron تلقائياً
echo.
echo ⚠️  للإيقاف: اضغط Ctrl+C
echo.

npm run electron-dev
