# 🖥️ دليل تطبيق سطح المكتب

## نظرة عامة

تم تحويل نظام إدارة الشؤون المالية المدرسية إلى تطبيق سطح مكتب باستخدام Electron، مما يتيح:

- ✅ تشغيل التطبيق بدون متصفح
- ✅ إنشاء ملف .exe قابل للتثبيت
- ✅ عمل بدون اتصال إنترنت (بعد إعداد قاعدة البيانات)
- ✅ قوائم عربية مخصصة
- ✅ اختصارات لوحة المفاتيح

## 🚀 البدء السريع

### 1. تطوير واختبار التطبيق:
```bash
# تثبيت المكتبات
npm install

# تشغيل في وضع التطوير
npm run electron-dev
```

أو استخدم الملف المبسط:
```bash
dev-electron.bat
```

### 2. بناء ملف .exe:
```bash
# بناء التطبيق كاملاً
npm run dist
```

أو استخدم الملف المبسط:
```bash
build-exe.bat
```

## 📋 متطلبات النظام

### للتطوير:
- Node.js 18+ 
- npm أو yarn
- Windows 10/11 (لبناء .exe)

### للمستخدم النهائي:
- Windows 10/11
- 4GB RAM (الحد الأدنى)
- 500MB مساحة فارغة

## 🔧 الأوامر المتاحة

| الأمر | الوصف |
|-------|--------|
| `npm run electron` | تشغيل Electron مع التطبيق المبني |
| `npm run electron-dev` | تطوير مع Hot Reload |
| `npm run build-electron` | بناء كامل مع التصدير |
| `npm run dist` | إنشاء ملف .exe للتوزيع |
| `npm run pack` | بناء بدون ضغط |

## 📁 هيكل الملفات الجديدة

```
project/
├── electron/
│   ├── main.js          # العملية الرئيسية لـ Electron
│   ├── preload.js       # سكريبت الأمان
│   └── assets/          # الأيقونات والموارد
├── dist/                # ملفات التوزيع (.exe)
├── out/                 # ملفات Next.js المصدرة
├── build-exe.bat        # بناء تلقائي
└── dev-electron.bat     # تطوير تلقائي
```

## 🎨 تخصيص الأيقونة

### إضافة أيقونة مخصصة:
1. أنشئ أيقونة 512x512 بكسل
2. احفظها بالصيغ التالية:
   - `electron/assets/icon.ico` (Windows)
   - `electron/assets/icon.icns` (macOS)
   - `electron/assets/icon.png` (Linux)

### أدوات مساعدة:
- [ICO Converter](https://convertio.co/png-ico/)
- [ICNS Converter](https://cloudconvert.com/png-to-icns)

## ⚙️ إعدادات البناء

تم تكوين `electron-builder` في `package.json`:

```json
"build": {
  "appId": "com.schoolfinance.app",
  "productName": "نظام إدارة الشؤون المالية المدرسية",
  "win": {
    "target": "nsis",
    "icon": "electron/assets/icon.ico"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true
  }
}
```

## 🔒 الأمان

### الميزات المطبقة:
- ✅ `contextIsolation: true`
- ✅ `nodeIntegration: false`
- ✅ `webSecurity: true`
- ✅ منع التنقل للمواقع الخارجية
- ✅ فتح الروابط في المتصفح الخارجي

## 📦 التوزيع

### ملفات الإخراج:
بعد تشغيل `npm run dist`:

```
dist/
├── نظام إدارة الشؤون المالية المدرسية Setup.exe  # ملف التثبيت
├── win-unpacked/                                      # ملفات غير مضغوطة
└── latest.yml                                         # معلومات التحديث
```

### تثبيت التطبيق:
1. شغّل `Setup.exe`
2. اختر مجلد التثبيت
3. اتبع التعليمات
4. سيتم إنشاء اختصار على سطح المكتب

## 🔄 التحديثات التلقائية

لإضافة التحديثات التلقائية (اختياري):

```bash
npm install electron-updater
```

ثم أضف في `main.js`:
```javascript
const { autoUpdater } = require('electron-updater');
autoUpdater.checkForUpdatesAndNotify();
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### ❌ "electron command not found"
```bash
npm install electron --save-dev
```

#### ❌ "build failed"
- تأكد من تشغيل `npm run build` أولاً
- تحقق من وجود مجلد `out/`

#### ❌ "icon not found"
- أضف ملف `electron/assets/icon.ico`
- أو احذف مرجع الأيقونة من `package.json`

#### ❌ التطبيق لا يعمل بعد البناء
- تحقق من إعدادات `next.config.js`
- تأكد من `output: 'export'`

### سجلات التشخيص:
```bash
# تشغيل مع سجلات مفصلة
DEBUG=electron-builder npm run dist
```

## 📱 منصات أخرى

### macOS:
```bash
npm run dist -- --mac
```

### Linux:
```bash
npm run dist -- --linux
```

## 🎯 نصائح للأداء

1. **تحسين الحجم**:
   - استخدم `npm run pack` للاختبار
   - احذف الملفات غير المطلوبة

2. **تسريع البناء**:
   - استخدم `--dir` للبناء السريع
   - فعّل cache في electron-builder

3. **تحسين الذاكرة**:
   - قلل حجم النافذة الافتراضي
   - استخدم lazy loading

## 📞 الدعم

للمساعدة في مشاكل Electron:
- [Electron Documentation](https://www.electronjs.org/docs)
- [electron-builder Guide](https://www.electron.build/)
- ملف `TROUBLESHOOTING.md` للمشاكل العامة
