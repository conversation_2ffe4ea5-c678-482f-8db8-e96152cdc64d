'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { ArrowRight, Plus, Printer, Save, Trash2, Edit } from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import { transactionsService, schoolInfoService } from '@/lib/supabase'
import { DailyTransaction, SchoolInfo } from '@/types/database'

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<DailyTransaction[]>([])
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [editingTransaction, setEditingTransaction] = useState<DailyTransaction | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    transaction_type: '',
    income: 0,
    expense: 0,
    transaction_date: new Date().toISOString().split('T')[0],
    document_number: ''
  })
  const printRef = useRef<HTMLDivElement>(null)

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'دفتر العمليات اليومية'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [transactionsData, schoolData] = await Promise.all([
        transactionsService.getTransactions(),
        schoolInfoService.getSchoolInfo()
      ])
      setTransactions(transactionsData)
      setSchoolInfo(schoolData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateBalance = (index: number) => {
    let balance = 0
    for (let i = transactions.length - 1; i >= index; i--) {
      const transaction = transactions[i]
      balance += (transaction.income || 0) - (transaction.expense || 0)
    }
    return balance
  }

  const handleSave = async () => {
    try {
      if (!formData.transaction_type || (!formData.income && !formData.expense)) {
        alert('يرجى إدخال جميع البيانات المطلوبة')
        return
      }

      const previousBalance = transactions.length > 0 ? transactions[0].remaining_balance : 0
      const newBalance = previousBalance + (formData.income || 0) - (formData.expense || 0)

      const transactionData = {
        ...formData,
        income: formData.income || null,
        expense: formData.expense || null,
        remaining_balance: newBalance,
        academic_year: schoolInfo?.academic_year || new Date().getFullYear() + '-' + (new Date().getFullYear() + 1),
        ...(editingTransaction && { id: editingTransaction.id })
      }

      await transactionsService.upsertTransaction(transactionData)
      await loadData()
      resetForm()
      alert('تم حفظ البيانات بنجاح')
    } catch (error) {
      console.error('Error saving transaction:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    }
  }

  const handleEdit = (transaction: DailyTransaction) => {
    setEditingTransaction(transaction)
    setFormData({
      transaction_type: transaction.transaction_type,
      income: transaction.income || 0,
      expense: transaction.expense || 0,
      transaction_date: transaction.transaction_date.split('T')[0],
      document_number: transaction.document_number || ''
    })
    setShowAddForm(true)
  }

  const handleDelete = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
      try {
        await transactionsService.deleteTransaction(id)
        await loadData()
        alert('تم حذف المعاملة بنجاح')
      } catch (error) {
        console.error('Error deleting transaction:', error)
        alert('حدث خطأ أثناء حذف المعاملة')
      }
    }
  }

  const resetForm = () => {
    setFormData({
      transaction_type: '',
      income: 0,
      expense: 0,
      transaction_date: new Date().toISOString().split('T')[0],
      document_number: ''
    })
    setEditingTransaction(null)
    setShowAddForm(false)
  }

  const getCurrentBalance = () => {
    return transactions.length > 0 ? transactions[0].remaining_balance : 0
  }

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('ar-TN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b no-print">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/financial" 
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للشؤون المالية
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900">دفتر العمليات اليومية</h1>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <button
                onClick={() => setShowAddForm(true)}
                className="btn-primary flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="h-4 w-4" />
                <span>إضافة معاملة</span>
              </button>
              <button
                onClick={handlePrint}
                className="btn-secondary flex items-center space-x-2 space-x-reverse"
              >
                <Printer className="h-4 w-4" />
                <span>طباعة</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="card mb-8 no-print">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {editingTransaction ? 'تعديل المعاملة' : 'إضافة معاملة جديدة'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
              <div>
                <label className="form-label">نوع العملية *</label>
                <input
                  type="text"
                  value={formData.transaction_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, transaction_type: e.target.value }))}
                  className="form-input"
                  required
                  placeholder="وصف العملية"
                />
              </div>
              <div>
                <label className="form-label">المقابيض (دينار)</label>
                <input
                  type="number"
                  step="0.001"
                  value={formData.income}
                  onChange={(e) => setFormData(prev => ({ ...prev, income: parseFloat(e.target.value) || 0 }))}
                  className="form-input"
                  min="0"
                />
              </div>
              <div>
                <label className="form-label">المصاريف (دينار)</label>
                <input
                  type="number"
                  step="0.001"
                  value={formData.expense}
                  onChange={(e) => setFormData(prev => ({ ...prev, expense: parseFloat(e.target.value) || 0 }))}
                  className="form-input"
                  min="0"
                />
              </div>
              <div>
                <label className="form-label">التاريخ *</label>
                <input
                  type="date"
                  value={formData.transaction_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, transaction_date: e.target.value }))}
                  className="form-input"
                  required
                />
              </div>
              <div>
                <label className="form-label">رقم الوثيقة</label>
                <input
                  type="text"
                  value={formData.document_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, document_number: e.target.value }))}
                  className="form-input"
                  placeholder="رقم الوثيقة المؤيدة"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 space-x-reverse">
              <button
                onClick={resetForm}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="btn-primary flex items-center space-x-2 space-x-reverse"
              >
                <Save className="h-4 w-4" />
                <span>حفظ</span>
              </button>
            </div>
          </div>
        )}

        {/* Current Balance Display */}
        <div className="card mb-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              الرصيد في {getCurrentDate()}
            </h3>
            <p className="text-3xl font-bold text-blue-600">
              {getCurrentBalance().toFixed(3)} دينار
            </p>
          </div>
        </div>

        {/* Printable Content */}
        <div ref={printRef} className="bg-white">
          {/* Print Header */}
          <div className="print-title">
            جمعية العمل التنموي - مشروع التصرّف الماليّ لسنة {schoolInfo?.academic_year} بـالمدرسة الإبتدائية {schoolInfo?.school_name}
          </div>
          <div className="print-subtitle">
            دفتر العمليات اليومية - الرصيد في {getCurrentDate()}: {getCurrentBalance().toFixed(3)} دينار
          </div>

          {/* Transactions Table */}
          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th>نوع العملية</th>
                  <th>المقابيض</th>
                  <th>المصاريف</th>
                  <th>الباقي</th>
                  <th>التاريخ</th>
                  <th>رقم الوثيقة المؤيدة</th>
                  <th className="no-print">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map((transaction, index) => (
                  <tr key={transaction.id}>
                    <td className="font-medium">{transaction.transaction_type}</td>
                    <td className="text-center">{transaction.income ? transaction.income.toFixed(3) : '-'}</td>
                    <td className="text-center">{transaction.expense ? transaction.expense.toFixed(3) : '-'}</td>
                    <td className="text-center font-semibold">{transaction.remaining_balance.toFixed(3)}</td>
                    <td className="text-center">
                      {new Date(transaction.transaction_date).toLocaleDateString('ar-TN')}
                    </td>
                    <td className="text-center">{transaction.document_number || '-'}</td>
                    <td className="no-print">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleEdit(transaction)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(transaction.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  )
}
