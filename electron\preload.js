const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للعملية الرئيسية
contextBridge.exposeInMainWorld('electronAPI', {
  // وظائف النظام
  platform: process.platform,
  
  // وظائف الطباعة
  print: () => ipcRenderer.invoke('print'),
  
  // وظائف الملفات
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  openFile: () => ipcRenderer.invoke('open-file'),
  
  // وظائف النوافذ
  minimize: () => ipcRenderer.invoke('minimize'),
  maximize: () => ipcRenderer.invoke('maximize'),
  close: () => ipcRenderer.invoke('close'),
  
  // معلومات التطبيق
  getVersion: () => ipcRenderer.invoke('get-version'),
  
  // إشعارات
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body)
});

// منع تحميل scripts خارجية
window.addEventListener('DOMContentLoaded', () => {
  // إضافة معلومات التطبيق
  const appInfo = document.createElement('meta');
  appInfo.name = 'app-type';
  appInfo.content = 'electron';
  document.head.appendChild(appInfo);
});
