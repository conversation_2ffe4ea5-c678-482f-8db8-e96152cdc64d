'use client'

import Link from 'next/link'
import { School, Calculator, FileText, Settings } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-primary-600 p-3 rounded-lg">
                <School className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  نظام إدارة الشؤون المالية المدرسية
                </h1>
                <p className="text-gray-600">
                  نظام شامل لإدارة الشؤون المالية للمدارس الابتدائية
                </p>
              </div>
            </div>
            <div className="bg-gray-100 p-2 rounded-lg">
              <img 
                src="/api/placeholder/80/80" 
                alt="شعار المدرسة" 
                className="h-16 w-16 object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            مرحباً بك في نظام إدارة الشؤون المالية
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            يمكنك من خلال هذا النظام إدارة جميع الشؤون المالية للمدرسة بطريقة منظمة وفعالة
          </p>
        </div>

        {/* Main Navigation Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* School Information Card */}
          <Link href="/school-info" className="group">
            <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="bg-green-100 p-4 rounded-full group-hover:bg-green-200 transition-colors">
                  <Settings className="h-8 w-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">معطيات المدرسة</h3>
                  <p className="text-gray-600">إدارة معلومات المدرسة والفترات الدراسية</p>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  معلومات المدرسة الأساسية
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  بيانات الإدارة والجمعية
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  الفترات الدراسية والتواريخ
                </div>
              </div>
            </div>
          </Link>

          {/* Financial Management Card */}
          <Link href="/financial" className="group">
            <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105">
              <div className="flex items-center space-x-4 space-x-reverse mb-6">
                <div className="bg-blue-100 p-4 rounded-full group-hover:bg-blue-200 transition-colors">
                  <Calculator className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">الشؤون المالية</h3>
                  <p className="text-gray-600">إدارة المداخيل والمصاريف والتقارير المالية</p>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full ml-2"></span>
                  جدول المداخيل
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full ml-2"></span>
                  دفتر العمليات اليومية
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full ml-2"></span>
                  التقارير المالية
                </div>
              </div>
            </div>
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm text-gray-600">إجمالي المداخيل</p>
                <p className="text-2xl font-bold text-gray-900">---</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center">
              <div className="bg-red-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-red-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm text-gray-600">إجمالي المصاريف</p>
                <p className="text-2xl font-bold text-gray-900">---</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm text-gray-600">الرصيد المتبقي</p>
                <p className="text-2xl font-bold text-gray-900">---</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>© 2024 نظام إدارة الشؤون المالية المدرسية. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
