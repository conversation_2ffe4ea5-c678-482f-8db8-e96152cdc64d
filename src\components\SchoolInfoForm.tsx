'use client'

import { useState, useEffect } from 'react'
import { Save } from 'lucide-react'
import { SchoolInfo } from '@/types/database'

interface SchoolInfoFormProps {
  initialData: SchoolInfo | null
  onSave: (data: any) => Promise<void>
  saving: boolean
}

export default function SchoolInfoForm({ initialData, onSave, saving }: SchoolInfoFormProps) {
  const [formData, setFormData] = useState({
    school_name: '',
    state_name: '',
    district_name: '',
    principal_name: '',
    association_president: '',
    treasurer_name: '',
    number_of_classes: 0,
    number_of_students: 0,
    academic_year: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)
  })

  useEffect(() => {
    if (initialData) {
      setFormData({
        school_name: initialData.school_name || '',
        state_name: initialData.state_name || '',
        district_name: initialData.district_name || '',
        principal_name: initialData.principal_name || '',
        association_president: initialData.association_president || '',
        treasurer_name: initialData.treasurer_name || '',
        number_of_classes: initialData.number_of_classes || 0,
        number_of_students: initialData.number_of_students || 0,
        academic_year: initialData.academic_year || new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)
      })
    }
  }, [initialData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSave(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* School Name */}
        <div>
          <label className="form-label">
            اسم المدرسة *
          </label>
          <input
            type="text"
            name="school_name"
            value={formData.school_name}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="أدخل اسم المدرسة"
          />
        </div>

        {/* State Name */}
        <div>
          <label className="form-label">
            اسم الولاية *
          </label>
          <input
            type="text"
            name="state_name"
            value={formData.state_name}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="أدخل اسم الولاية"
          />
        </div>

        {/* District Name */}
        <div>
          <label className="form-label">
            اسم المعتمدية *
          </label>
          <input
            type="text"
            name="district_name"
            value={formData.district_name}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="أدخل اسم المعتمدية"
          />
        </div>

        {/* Academic Year */}
        <div>
          <label className="form-label">
            السنة الدراسية *
          </label>
          <input
            type="text"
            name="academic_year"
            value={formData.academic_year}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="مثال: 2024-2025"
          />
        </div>

        {/* Principal Name */}
        <div>
          <label className="form-label">
            اسم مدير المدرسة *
          </label>
          <input
            type="text"
            name="principal_name"
            value={formData.principal_name}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="أدخل اسم مدير المدرسة"
          />
        </div>

        {/* Association President */}
        <div>
          <label className="form-label">
            اسم رئيس الجمعية *
          </label>
          <input
            type="text"
            name="association_president"
            value={formData.association_president}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="أدخل اسم رئيس الجمعية"
          />
        </div>

        {/* Treasurer Name */}
        <div>
          <label className="form-label">
            اسم أمين المال *
          </label>
          <input
            type="text"
            name="treasurer_name"
            value={formData.treasurer_name}
            onChange={handleChange}
            className="form-input"
            required
            placeholder="أدخل اسم أمين المال"
          />
        </div>

        {/* Number of Classes */}
        <div>
          <label className="form-label">
            عدد الفصول *
          </label>
          <input
            type="number"
            name="number_of_classes"
            value={formData.number_of_classes}
            onChange={handleChange}
            className="form-input"
            required
            min="1"
            placeholder="أدخل عدد الفصول"
          />
        </div>

        {/* Number of Students */}
        <div className="md:col-span-2">
          <label className="form-label">
            عدد التلاميذ *
          </label>
          <input
            type="number"
            name="number_of_students"
            value={formData.number_of_students}
            onChange={handleChange}
            className="form-input"
            required
            min="1"
            placeholder="أدخل عدد التلاميذ"
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200">
        <button
          type="submit"
          disabled={saving}
          className="btn-primary flex items-center space-x-2 space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>جاري الحفظ...</span>
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              <span>حفظ المعلومات</span>
            </>
          )}
        </button>
      </div>
    </form>
  )
}
