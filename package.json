{"name": "school-finance-app", "version": "0.1.0", "private": true, "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "build-electron": "npm run build && npm run export && electron-builder", "export": "next export", "dist": "npm run build && npm run export && electron-builder --publish=never", "pack": "electron-builder --dir"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.4", "@supabase/auth-helpers-nextjs": "^0.8.7", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "react-to-print": "^2.14.15", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4", "electron": "^27.1.3", "electron-builder": "^24.6.4", "concurrently": "^8.2.2", "wait-on": "^7.2.0"}, "build": {"appId": "com.schoolfinance.app", "productName": "نظام إدارة الشؤون المالية المدرسية", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "package.json"], "win": {"target": "nsis", "icon": "electron/assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام الشؤون المالية المدرسية"}, "mac": {"target": "dmg", "icon": "electron/assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "electron/assets/icon.png"}}}