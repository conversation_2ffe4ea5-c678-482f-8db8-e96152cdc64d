import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Helper functions for database operations
export const schoolInfoService = {
  async getSchoolInfo() {
    const { data, error } = await supabase
      .from('school_info')
      .select('*')
      .single()
    
    if (error && error.code !== 'PGRST116') {
      throw error
    }
    
    return data
  },

  async upsertSchoolInfo(schoolInfo: any) {
    const { data, error } = await supabase
      .from('school_info')
      .upsert(schoolInfo)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

export const academicPeriodsService = {
  async getAcademicPeriods() {
    const { data, error } = await supabase
      .from('academic_periods')
      .select('*')
      .order('period_number')
    
    if (error) throw error
    return data || []
  },

  async upsertAcademicPeriod(period: any) {
    const { data, error } = await supabase
      .from('academic_periods')
      .upsert(period)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

export const incomeService = {
  async getIncomeRecords() {
    const { data, error } = await supabase
      .from('income_records')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  async upsertIncomeRecord(record: any) {
    const { data, error } = await supabase
      .from('income_records')
      .upsert(record)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async deleteIncomeRecord(id: string) {
    const { error } = await supabase
      .from('income_records')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

export const transactionsService = {
  async getTransactions() {
    const { data, error } = await supabase
      .from('daily_transactions')
      .select('*')
      .order('transaction_date', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  async upsertTransaction(transaction: any) {
    const { data, error } = await supabase
      .from('daily_transactions')
      .upsert(transaction)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async deleteTransaction(id: string) {
    const { error } = await supabase
      .from('daily_transactions')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}
