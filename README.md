# نظام إدارة الشؤون المالية المدرسية

نظام شامل لإدارة الشؤون المالية للمدارس الابتدائية باللغة العربية مع دعم RTL.

## المميزات

### معطيات المدرسة
- إدارة معلومات المدرسة الأساسية
- بيانات الإدارة والجمعية
- إدارة الفترات الدراسية (الثلاثيات الأربعة)
- تحديد تواريخ البداية والنهاية لكل فترة

### الشؤون المالية
- **جدول المداخيل**: إدارة مصادر الدخل المختلفة
- **دفتر العمليات اليومية**: تسجيل المعاملات اليومية مع حساب الرصيد
- **التقرير المالي**: تقارير شاملة للموارد والنفقات

### المميزات التقنية
- واجهة باللغة العربية مع دعم RTL
- تصميم متجاوب لجميع الأجهزة
- طباعة وتصدير التقارير
- حفظ البيانات في قاعدة بيانات Supabase
- تحديث الحسابات في الوقت الفعلي
- **تطبيق سطح مكتب**: ملف .exe قابل للتثبيت
- قوائم عربية مخصصة واختصارات لوحة المفاتيح

## التقنيات المستخدمة

- **Frontend**: Next.js 14 with TypeScript
- **Database**: Supabase
- **Styling**: Tailwind CSS with RTL support
- **Desktop App**: Electron with electron-builder
- **UI Components**: Lucide React Icons
- **Print**: React-to-Print
- **Forms**: React Hook Form

## التثبيت والإعداد

### 1. متطلبات النظام
- Node.js 18+ 
- npm أو yarn
- حساب Supabase

### 2. إعداد المشروع

```bash
# تحميل المشروع
git clone <repository-url>
cd school-finance-app

# تثبيت المكتبات
npm install

# أو باستخدام yarn
yarn install
```

### 3. إعداد Supabase

1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. نسخ URL المشروع و Anon Key
3. تحديث ملف `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. تشغيل SQL Schema في Supabase SQL Editor:
   - افتح Supabase Dashboard
   - اذهب إلى SQL Editor
   - انسخ محتوى ملف `supabase-schema.sql` وشغله

### 4. تشغيل المشروع

```bash
# تشغيل الخادم المحلي
npm run dev

# أو باستخدام yarn
yarn dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## 🖥️ تطبيق سطح المكتب

### تشغيل كتطبيق سطح مكتب:

```bash
# تطوير واختبار
npm run electron-dev

# أو استخدم الملف المبسط
dev-electron.bat
```

### بناء ملف .exe:

```bash
# بناء التطبيق للتوزيع
npm run dist

# أو استخدم الملف المبسط
build-exe.bat
```

### الملفات الناتجة:
- `dist/نظام إدارة الشؤون المالية المدرسية Setup.exe` - ملف التثبيت
- اختصار على سطح المكتب بعد التثبيت
- قوائم عربية مخصصة

📖 **للمزيد**: راجع `DESKTOP_APP_GUIDE.md`

## هيكل المشروع

```
src/
├── app/                    # Next.js App Router
│   ├── page.tsx           # الصفحة الرئيسية
│   ├── school-info/       # قسم معطيات المدرسة
│   └── financial/         # قسم الشؤون المالية
│       ├── income/        # جدول المداخيل
│       ├── transactions/  # دفتر العمليات اليومية
│       └── reports/       # التقارير المالية
├── components/            # المكونات المشتركة
├── lib/                   # مكتبات ووظائف مساعدة
└── types/                 # تعريفات TypeScript
```

## الاستخدام

### 1. إعداد معطيات المدرسة
- اذهب إلى قسم "معطيات المدرسة"
- أدخل معلومات المدرسة الأساسية
- حدد تواريخ الفترات الدراسية

### 2. إدارة المداخيل
- اذهب إلى "الشؤون المالية" > "جدول المداخيل"
- أضف مصادر الدخل المختلفة
- احفظ وطباعة الجدول

### 3. تسجيل المعاملات اليومية
- اذهب إلى "دفتر العمليات اليومية"
- سجل المقابيض والمصاريف
- راقب الرصيد المتبقي

### 4. إنشاء التقارير المالية
- اذهب إلى "التقرير المالي"
- اختر الفترة الدراسية
- اطبع أو صدر التقرير

## مصادر المداخيل المدعومة

- باب منحة الدّولة: التّعهّد و الصّيانة
- باب منحة الحليب لفائدة العامل
- باب التّبرّعات و الهدايا
- باب حصّة المدرسة من دروس التّدارك
- باب مداخيل متعلّقة بأنشطة مدرسيّة
- باب مداخيل كراء المشرب (إن وجد)

## فئات المصاريف

- الصّيـــانة و التّنظيف
- منحة الحليب لفائدة العامل
- تجهيزات و أدوات مكتبيّة
- جوائـــز و مكافئات و مصاريف تنشيط
- المصاريف المتعلّقة بالجمعيّة

## البناء للإنتاج

```bash
# بناء المشروع
npm run build

# تشغيل النسخة المبنية
npm start
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
