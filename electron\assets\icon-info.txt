# ملفات الأيقونات المطلوبة

لإنشاء ملف .exe بأيقونة مخصصة، تحتاج إلى إضافة الملفات التالية:

## الملفات المطلوبة:
- icon.ico (256x256 pixels) - لنظام Windows
- icon.icns (512x512 pixels) - لنظام macOS  
- icon.png (512x512 pixels) - لنظام Linux

## كيفية إنشاء الأيقونات:

### 1. تصميم الأيقونة:
- استخدم برنامج مثل GIMP أو Photoshop
- أنشئ تصميم بحجم 512x512 بكسل
- استخدم ألوان تمثل التعليم والمالية (أزرق، أخضر)
- احفظ كـ PNG عالي الجودة

### 2. تحويل إلى صيغ مختلفة:

#### لإنشاء .ico (Windows):
- استخدم موقع: https://convertio.co/png-ico/
- أو برنامج: IcoFX

#### لإنشاء .icns (macOS):
- استخدم موقع: https://cloudconvert.com/png-to-icns
- أو أداة: png2icns

### 3. وضع الملفات:
ضع الملفات في مجلد electron/assets/:
- electron/assets/icon.ico
- electron/assets/icon.icns  
- electron/assets/icon.png

## أيقونة افتراضية مؤقتة:
إذا لم تكن لديك أيقونة مخصصة، سيستخدم Electron الأيقونة الافتراضية.

## نصائح للتصميم:
- استخدم رموز تعليمية (كتاب، قلم، حاسبة)
- ألوان هادئة ومهنية
- تجنب التفاصيل الدقيقة (ستكون صغيرة)
- اختبر الأيقونة بأحجام مختلفة
