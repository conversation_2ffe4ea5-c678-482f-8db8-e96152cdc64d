'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { ArrowR<PERSON>, Printer, FileText, Calendar } from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import { schoolInfoService, academicPeriodsService, incomeService, transactionsService } from '@/lib/supabase'
import { SchoolInfo, AcademicPeriod, IncomeRecord, DailyTransaction } from '@/types/database'

const EXPENSE_CATEGORIES = [
  'الصّيـــانة و التّنظيف',
  'منحة الحليب لفائدة العامل',
  'تجهيزات و أدوات مكتبيّة',
  'جوائـــز و مكافئات و مصاريف تنشيط',
  'المصاريف المتعلّقة بالجمعيّة'
]

export default function ReportsPage() {
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo | null>(null)
  const [academicPeriods, setAcademicPeriods] = useState<AcademicPeriod[]>([])
  const [incomeRecords, setIncomeRecords] = useState<IncomeRecord[]>([])
  const [transactions, setTransactions] = useState<DailyTransaction[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<number>(1)
  const [loading, setLoading] = useState(true)
  const printRef = useRef<HTMLDivElement>(null)

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'التقرير المالي'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [schoolData, periodsData, incomeData, transactionsData] = await Promise.all([
        schoolInfoService.getSchoolInfo(),
        academicPeriodsService.getAcademicPeriods(),
        incomeService.getIncomeRecords(),
        transactionsService.getTransactions()
      ])
      
      setSchoolInfo(schoolData)
      setAcademicPeriods(periodsData)
      setIncomeRecords(incomeData)
      setTransactions(transactionsData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSelectedPeriodInfo = () => {
    return academicPeriods.find(p => p.period_number === selectedPeriod)
  }

  const getTotalIncome = () => {
    return incomeRecords.reduce((total, record) => total + record.amount, 0)
  }

  const getExpensesByCategory = () => {
    const expenses: { [key: string]: number } = {}
    
    // Initialize all categories with 0
    EXPENSE_CATEGORIES.forEach(category => {
      expenses[category] = 0
    })

    // Calculate expenses from transactions
    transactions.forEach(transaction => {
      if (transaction.expense && transaction.expense > 0) {
        // Try to match transaction type to expense categories
        const matchedCategory = EXPENSE_CATEGORIES.find(category => 
          transaction.transaction_type.includes(category) ||
          category.includes(transaction.transaction_type)
        )
        
        if (matchedCategory) {
          expenses[matchedCategory] += transaction.expense
        } else {
          // If no match found, add to "other expenses" or first category
          expenses[EXPENSE_CATEGORIES[0]] += transaction.expense
        }
      }
    })

    return expenses
  }

  const getTotalExpenses = () => {
    const expenses = getExpensesByCategory()
    return Object.values(expenses).reduce((total, amount) => total + amount, 0)
  }

  const getRemainingBalance = () => {
    return getTotalIncome() - getTotalExpenses()
  }

  const getPreviousPeriodBalance = () => {
    // This would typically come from the previous period's data
    // For now, we'll return 0 or calculate based on available data
    return 0
  }

  const getTotalResources = () => {
    return getPreviousPeriodBalance() + getTotalIncome()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  const selectedPeriodInfo = getSelectedPeriodInfo()
  const totalIncome = getTotalIncome()
  const expensesByCategory = getExpensesByCategory()
  const totalExpenses = getTotalExpenses()
  const totalResources = getTotalResources()
  const remainingBalance = getRemainingBalance()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b no-print">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/financial" 
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للشؤون المالية
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900">التقرير المالي</h1>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(parseInt(e.target.value))}
                className="form-input w-auto"
              >
                {academicPeriods.map((period) => (
                  <option key={period.id} value={period.period_number}>
                    {period.period_name}
                  </option>
                ))}
              </select>
              <button
                onClick={handlePrint}
                className="btn-primary flex items-center space-x-2 space-x-reverse"
              >
                <Printer className="h-4 w-4" />
                <span>طباعة التقرير</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 no-print">
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الموارد</p>
                <p className="text-2xl font-bold text-green-600">{totalResources.toFixed(3)}</p>
                <p className="text-xs text-gray-500">دينار تونسي</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي النفقات</p>
                <p className="text-2xl font-bold text-red-600">{totalExpenses.toFixed(3)}</p>
                <p className="text-xs text-gray-500">دينار تونسي</p>
              </div>
              <div className="bg-red-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المبلغ المتبقي</p>
                <p className="text-2xl font-bold text-blue-600">{remainingBalance.toFixed(3)}</p>
                <p className="text-xs text-gray-500">دينار تونسي</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Printable Report */}
        <div ref={printRef} className="bg-white p-8">
          {/* Report Header */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-2">
              المدرسة الابتدائيّة {schoolInfo?.school_name}
            </h1>
            <h2 className="text-xl font-semibold mb-4">
              تقرير مالي
            </h2>
            <div className="text-lg">
              <p>الثلاثي: {selectedPeriodInfo?.period_name} لسنة: {schoolInfo?.academic_year}</p>
              {selectedPeriodInfo && (
                <p className="text-sm text-gray-600 mt-2">
                  من {new Date(selectedPeriodInfo.start_date).toLocaleDateString('ar-TN')} 
                  إلى {new Date(selectedPeriodInfo.end_date).toLocaleDateString('ar-TN')}
                </p>
              )}
            </div>
          </div>

          {/* Financial Report Table */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Resources Column */}
            <div>
              <h3 className="text-lg font-bold mb-4 text-center bg-green-100 p-2 rounded">
                الموارد
              </h3>
              <table className="w-full border border-gray-300">
                <tbody>
                  <tr>
                    <td className="border border-gray-300 p-2 font-medium">
                      الرصيد المتبقي من الثلاثي السابق
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {getPreviousPeriodBalance().toFixed(3)}
                    </td>
                  </tr>
                  {incomeRecords.map((record) => (
                    <tr key={record.id}>
                      <td className="border border-gray-300 p-2">
                        {record.category}
                      </td>
                      <td className="border border-gray-300 p-2 text-center">
                        {record.amount.toFixed(3)}
                      </td>
                    </tr>
                  ))}
                  <tr className="bg-green-50 font-bold">
                    <td className="border border-gray-300 p-2">
                      مجموع الموارد
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {totalResources.toFixed(3)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Expenses Column */}
            <div>
              <h3 className="text-lg font-bold mb-4 text-center bg-red-100 p-2 rounded">
                النفقات
              </h3>
              <table className="w-full border border-gray-300">
                <tbody>
                  {EXPENSE_CATEGORIES.map((category) => (
                    <tr key={category}>
                      <td className="border border-gray-300 p-2">
                        {category}
                      </td>
                      <td className="border border-gray-300 p-2 text-center">
                        {expensesByCategory[category].toFixed(3)}
                      </td>
                    </tr>
                  ))}
                  <tr className="bg-red-50 font-bold">
                    <td className="border border-gray-300 p-2">
                      مجموع النفقات
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {totalExpenses.toFixed(3)}
                    </td>
                  </tr>
                  <tr className="bg-blue-50 font-bold">
                    <td className="border border-gray-300 p-2">
                      المبلغ المتبقي
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {remainingBalance.toFixed(3)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Signatures Section */}
          <div className="mt-12 grid grid-cols-3 gap-8 text-center">
            <div>
              <p className="font-semibold mb-8">رئيس الجمعيّة</p>
              <p className="border-t border-gray-400 pt-2">
                {schoolInfo?.association_president}
              </p>
            </div>
            <div>
              <p className="font-semibold mb-8">أمين مال الجمعيّة</p>
              <p className="border-t border-gray-400 pt-2">
                {schoolInfo?.treasurer_name}
              </p>
            </div>
            <div>
              <p className="font-semibold mb-8">مدير المدرسة</p>
              <p className="border-t border-gray-400 pt-2">
                {schoolInfo?.principal_name}
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
