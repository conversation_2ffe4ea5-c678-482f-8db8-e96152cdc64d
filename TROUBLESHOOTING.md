# 🔧 حل المشاكل الشائعة

## مشاكل Node.js

### ❌ "node is not recognized"
**السبب**: Node.js غير مثبت أو غير مضاف لـ PATH

**الحل**:
1. حمّل Node.js من https://nodejs.org
2. ثبّت البرنامج (تأكد من اختيار "Add to PATH")
3. أعد تشغيل Command Prompt
4. اختبر: `node --version`

### ❌ "npm is not recognized"
**السبب**: npm لم يُثبت مع Node.js

**الحل**:
1. أعد تثبيت Node.js
2. تأكد من اختيار جميع المكونات
3. أعد تشغيل الجهاز

## مشاكل Supabase

### ❌ "Invalid API key"
**السبب**: مفتاح API خاطئ أو منتهي الصلاحية

**الحل**:
1. اذهب إلى Supabase Dashboard
2. Settings > API
3. ان<PERSON>خ "anon public" key جديد
4. حدّث .env.local

### ❌ "Failed to fetch"
**السبب**: URL خاطئ أو مشكلة في الشبكة

**الحل**:
1. تحقق من Project URL في Supabase
2. تأكد من عدم وجود مسافات في .env.local
3. تحقق من اتصال الإنترنت

### ❌ "Table doesn't exist"
**السبب**: لم يتم تشغيل SQL Schema

**الحل**:
1. اذهب إلى Supabase SQL Editor
2. انسخ محتوى `supabase-schema.sql`
3. شغّل الاستعلام كاملاً
4. تحقق من Table Editor

## مشاكل المشروع

### ❌ "Module not found"
**السبب**: المكتبات غير مثبتة

**الحل**:
```bash
# امسح المجلد وأعد التثبيت
rm -rf node_modules
rm package-lock.json
npm install
```

### ❌ "Port 3000 is already in use"
**السبب**: المنفذ مستخدم من برنامج آخر

**الحل**:
```bash
# استخدم منفذ آخر
npm run dev -- -p 3001

# أو أوقف العملية المستخدمة للمنفذ
netstat -ano | findstr :3000
taskkill /PID [رقم العملية] /F
```

### ❌ صفحة فارغة أو خطأ في التحميل
**السبب**: مشكلة في ملفات المشروع

**الحل**:
1. تحقق من Console في المتصفح (F12)
2. تأكد من وجود جميع الملفات
3. أعد تشغيل الخادم

## مشاكل البيانات

### ❌ "Permission denied" في Supabase
**السبب**: مشكلة في RLS policies

**الحل**:
```sql
-- في Supabase SQL Editor
ALTER TABLE school_info DISABLE ROW LEVEL SECURITY;
ALTER TABLE academic_periods DISABLE ROW LEVEL SECURITY;
ALTER TABLE income_records DISABLE ROW LEVEL SECURITY;
ALTER TABLE daily_transactions DISABLE ROW LEVEL SECURITY;
```

### ❌ البيانات لا تُحفظ
**السبب**: مشكلة في الاتصال أو الصلاحيات

**الحل**:
1. تحقق من Network tab في المتصفح
2. راجع Supabase Logs
3. تأكد من صحة API credentials

## مشاكل الطباعة

### ❌ التقرير لا يُطبع بشكل صحيح
**السبب**: مشكلة في CSS للطباعة

**الحل**:
1. استخدم Chrome أو Edge للطباعة
2. اختر "More settings" > "Options" > "Headers and footers" (أزل العلامة)
3. اختر حجم ورق A4

### ❌ النص العربي معكوس في الطباعة
**السبب**: مشكلة في دعم RTL

**الحل**:
1. تأكد من تحديث المتصفح
2. استخدم "Print to PDF" أولاً
3. اطبع من PDF

## أدوات التشخيص

### فحص سريع للإعداد:
```bash
# شغّل هذا الملف
check-setup.bat
```

### فحص الاتصال بـ Supabase:
1. افتح المتصفح
2. اذهب إلى http://localhost:3000
3. افتح Developer Tools (F12)
4. راقب Console للأخطاء

### فحص قاعدة البيانات:
1. اذهب إلى Supabase Dashboard
2. Table Editor
3. تحقق من وجود البيانات

## طلب المساعدة

إذا لم تحل المشكلة:

1. **اجمع المعلومات**:
   - نسخة Node.js: `node --version`
   - نسخة npm: `npm --version`
   - نظام التشغيل
   - رسالة الخطأ كاملة

2. **تحقق من**:
   - Console في المتصفح
   - Terminal output
   - Supabase Logs

3. **جرب**:
   - إعادة تشغيل الخادم
   - مسح cache المتصفح
   - تحديث المكتبات

## نصائح للوقاية

1. **احفظ نسخة احتياطية** من .env.local
2. **لا تحذف** node_modules إلا عند الضرورة
3. **حدّث المكتبات** بانتظام
4. **راقب Supabase usage** لتجنب تجاوز الحدود
5. **استخدم Git** لحفظ التغييرات
