-- School Financial Management System Database Schema
-- Run this SQL in your Supabase SQL Editor to create the required tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- School Information Table
CREATE TABLE IF NOT EXISTS school_info (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    school_name VARCHAR(255) NOT NULL,
    state_name VARCHAR(255) NOT NULL,
    district_name VA<PERSON>HAR(255) NOT NULL,
    principal_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    association_president <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    treasurer_name VARCHAR(255) NOT NULL,
    number_of_classes INTEGER NOT NULL DEFAULT 0,
    number_of_students INTEGER NOT NULL DEFAULT 0,
    academic_year VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Academic Periods Table
CREATE TABLE IF NOT EXISTS academic_periods (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    period_number INTEGER NOT NULL,
    period_name VA<PERSON>HAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(period_number, academic_year)
);

-- Income Records Table
CREATE TABLE IF NOT EXISTS income_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    category VARCHAR(255) NOT NULL,
    amount DECIMAL(10,3) NOT NULL DEFAULT 0,
    notes TEXT,
    academic_year VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily Transactions Table
CREATE TABLE IF NOT EXISTS daily_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    transaction_type VARCHAR(255) NOT NULL,
    income DECIMAL(10,3),
    expense DECIMAL(10,3),
    remaining_balance DECIMAL(10,3) NOT NULL DEFAULT 0,
    transaction_date DATE NOT NULL,
    document_number VARCHAR(100),
    academic_year VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_academic_periods_year ON academic_periods(academic_year);
CREATE INDEX IF NOT EXISTS idx_income_records_year ON income_records(academic_year);
CREATE INDEX IF NOT EXISTS idx_daily_transactions_year ON daily_transactions(academic_year);
CREATE INDEX IF NOT EXISTS idx_daily_transactions_date ON daily_transactions(transaction_date);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_school_info_updated_at BEFORE UPDATE ON school_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_academic_periods_updated_at BEFORE UPDATE ON academic_periods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_income_records_updated_at BEFORE UPDATE ON income_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_transactions_updated_at BEFORE UPDATE ON daily_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO school_info (
    school_name,
    state_name,
    district_name,
    principal_name,
    association_president,
    treasurer_name,
    number_of_classes,
    number_of_students,
    academic_year
) VALUES (
    'المدرسة الابتدائية النموذجية',
    'تونس',
    'تونس',
    'أحمد بن محمد',
    'فاطمة بن علي',
    'محمد بن سالم',
    12,
    300,
    '2024-2025'
) ON CONFLICT DO NOTHING;

-- Insert sample academic periods
INSERT INTO academic_periods (period_number, period_name, start_date, end_date, academic_year) VALUES
(1, 'الثلاثي الأول', '2024-09-15', '2024-12-20', '2024-2025'),
(2, 'الثلاثي الثاني', '2025-01-07', '2025-03-28', '2024-2025'),
(3, 'الثلاثي الثالث', '2025-04-07', '2025-06-20', '2024-2025'),
(4, 'الثلاثي الرابع', '2025-06-21', '2025-07-15', '2024-2025')
ON CONFLICT (period_number, academic_year) DO NOTHING;

-- Insert sample income records
INSERT INTO income_records (category, amount, notes, academic_year) VALUES
('باب منحة الدّولة: التّعهّد و الصّيانة', 1500.000, 'منحة الدولة للصيانة', '2024-2025'),
('باب منحة الحليب لفائدة العامل', 200.000, 'منحة الحليب الشهرية', '2024-2025'),
('باب التّبرّعات و الهدايا', 300.000, 'تبرعات من أولياء التلاميذ', '2024-2025'),
('باب حصّة المدرسة من دروس التّدارك', 150.000, 'حصة من دروس التدارك', '2024-2025'),
('باب مداخيل متعلّقة بأنشطة مدرسيّة', 100.000, 'أنشطة ثقافية ورياضية', '2024-2025')
ON CONFLICT DO NOTHING;

-- Insert sample transactions
INSERT INTO daily_transactions (transaction_type, income, expense, remaining_balance, transaction_date, document_number, academic_year) VALUES
('استلام منحة الدولة', 1500.000, NULL, 1500.000, '2024-09-15', 'DOC001', '2024-2025'),
('شراء مواد تنظيف', NULL, 50.000, 1450.000, '2024-09-20', 'DOC002', '2024-2025'),
('استلام تبرعات', 300.000, NULL, 1750.000, '2024-09-25', 'DOC003', '2024-2025'),
('صيانة المرافق', NULL, 200.000, 1550.000, '2024-10-01', 'DOC004', '2024-2025')
ON CONFLICT DO NOTHING;

-- Enable Row Level Security (RLS)
ALTER TABLE school_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE income_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_transactions ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)
CREATE POLICY "Allow all operations on school_info" ON school_info FOR ALL USING (true);
CREATE POLICY "Allow all operations on academic_periods" ON academic_periods FOR ALL USING (true);
CREATE POLICY "Allow all operations on income_records" ON income_records FOR ALL USING (true);
CREATE POLICY "Allow all operations on daily_transactions" ON daily_transactions FOR ALL USING (true);

-- Note: In a production environment, you should implement proper authentication
-- and create more restrictive RLS policies based on user roles and permissions.
