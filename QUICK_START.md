# 🚀 البدء السريع

## ما تحتاجه (5 دقائق):

### 1. تثبيت Node.js
- اذهب إلى https://nodejs.org
- حمّل النسخة LTS
- ثبّت البرنامج
- أعد تشغيل Command Prompt

### 2. إعداد Supabase
- اذهب إلى https://supabase.com
- أنشئ حساب جديد
- أنشئ مشروع جديد
- احصل على URL و API Key

### 3. إعداد المشروع
```bash
# في مجلد المشروع
npm install
```

### 4. تحديث ملف .env.local
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key_here
```

### 5. إعداد قاعدة البيانات
- في Supabase SQL Editor
- ان<PERSON><PERSON> محتوى `supabase-schema.sql`
- شغّل الاستعلام

### 6. تشغيل المشروع
```bash
npm run dev
```

## أو استخدم الملف التلقائي:
```bash
# في Windows
start.bat

# أو يدوياً
npm run dev
```

## 🎯 النتيجة المتوقعة:
- صفحة رئيسية باللغة العربية
- قسم معطيات المدرسة يعمل
- قسم الشؤون المالية يعمل
- إمكانية الطباعة

## 📞 المساعدة:
إذا واجهت مشاكل، راجع `INSTALLATION_GUIDE.md` للتفاصيل الكاملة.
